'use client'

import React, { useState, useRef, useEffect } from 'react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useAppStore, useActiveTab, useActiveChatMessages } from '@/lib/store'
import { Send, Brain, MessageCircle, Sparkles } from 'lucide-react'

const AIAssistant: React.FC = () => {
  const { addChatMessage, updateChatMessage } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const [chatInput, setChatInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到聊天底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [chatMessages, isStreaming])

  // 处理流式聊天
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab || isStreaming) return

    const userMessage = chatInput.trim()
    setChatInput('')

    try {
      // 添加用户消息
      addChatMessage(activeTab.id, {
        role: 'user',
        content: userMessage
      })

      setIsStreaming(true)

      // 添加AI消息占位符
      const aiMessageId = addChatMessage(activeTab.id, {
        role: 'assistant',
        content: ''
      })

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          context: {
            originalContent: activeTab.originalContent,
            aiNote: activeTab.aiNoteMarkdown
          }
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      let accumulatedContent = ''
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              setIsStreaming(false)
              return
            }

            try {
              const parsed = JSON.parse(data)
              if (parsed.content) {
                accumulatedContent += parsed.content
                updateChatMessage(activeTab.id, aiMessageId, accumulatedContent)
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      console.error('聊天错误:', error)
      let errorMessage = '未知错误'

      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          errorMessage = '网络连接失败，请检查网络连接'
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请稍后重试'
        } else if (error.message.includes('401')) {
          errorMessage = 'API密钥无效，请检查配置'
        } else if (error.message.includes('429')) {
          errorMessage = '请求过于频繁，请稍后重试'
        } else {
          errorMessage = error.message
        }
      }

      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: `❌ **出现错误**\n\n${errorMessage}\n\n💡 **建议**：\n- 检查网络连接\n- 稍后重试\n- 如果问题持续，请联系管理员`
      })
    } finally {
      setIsStreaming(false)
    }
  }

  if (!activeTab) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 p-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <Brain className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
            <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col liquid-glass">
      {/* 结构化笔记 - 直接整合到AI助手顶部 */}
      <div className="flex-shrink-0 border-b border-gray-200/50">
        {/* 结构化笔记标题栏 */}
        <div className="px-4 py-3 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Sparkles className="w-4 h-4 text-blue-500" />
              <h3 className="font-semibold text-gray-900 text-sm">结构化笔记</h3>
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {activeTab.aiNoteMarkdown ? "已生成" : (activeTab.aiAnalyzing || activeTab.isLoading) ? "生成中" : "待分析"}
              </span>
            </div>
          </div>
        </div>
        
        {/* 结构化笔记内容 */}
        <div className="max-h-[40vh] min-h-[200px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 p-6 bg-white/50">
          {activeTab.isLoading || activeTab.aiAnalyzing || (!activeTab.aiNoteMarkdown && activeTab.sourceType === 'url') ? (
            <div className="py-8">
              <ModernLoader
                variant="dots"
                size="md"
                text={
                  activeTab.isLoading
                    ? "正在分析..."
                    : activeTab.aiAnalyzing
                      ? "正在生成结构化笔记..."
                      : "等待分析..."
                }
                className="text-center"
              />
              {(activeTab.aiAnalyzing || activeTab.sourceType === 'url') && !activeTab.isLoading && (
                <p className="text-xs text-gray-500 mt-3 text-center">
                  {activeTab.aiAnalyzing ? "正在流式生成结构化笔记，请稍候..." : "您可以先浏览网页，分析完成后会自动显示"}
                </p>
              )}
            </div>
          ) : activeTab.aiNoteMarkdown ? (
            <div className="ai-note-content">
              <div className="relative">
                <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:my-1 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:rounded">
                  {activeTab.aiNoteMarkdown}
                </SafeMarkdown>
                
                {/* 流式输入光标 */}
                {activeTab.aiAnalyzing && activeTab.aiNoteMarkdown && (
                  <span className="inline-block w-0.5 h-4 bg-blue-500 ml-1 animate-pulse"></span>
                )}
              </div>
              
              {/* 流式生成指示器 */}
              {activeTab.aiAnalyzing && (
                <div className="flex items-center space-x-2 mt-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-100"></div>
                    <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-200"></div>
                  </div>
                  <span className="text-xs text-blue-700 font-medium">正在流式生成中...</span>
                  <div className="ml-auto">
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="py-8 text-center">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
                <Brain className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-500 text-sm">暂无结构化笔记</p>
              <p className="text-gray-400 text-xs mt-1">系统将为您的内容生成结构化摘要</p>
            </div>
          )}
        </div>
      </div>

      {/* 聊天区域 - 占据剩余空间，输入框始终在底部 */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* AI对话标题栏 */}
        <div className="px-4 py-3 bg-gradient-to-r from-purple-50/50 to-blue-50/50 border-b border-gray-200/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <MessageCircle className="w-4 h-4 text-purple-500" />
              <h3 className="font-semibold text-gray-900 text-sm">AI对话</h3>
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                {chatMessages.length}
              </span>
            </div>
          </div>
        </div>
        
        {/* 聊天消息容器 - 可滚动，修复高度问题 */}
        <div
          ref={chatContainerRef}
          className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 bg-white/50"
          style={{ maxHeight: 'calc(100vh - 500px)', minHeight: '300px' }}
        >
          {chatMessages.length === 0 ? (
            <div className="flex items-center justify-center h-full min-h-[200px]">
              <div className="text-center space-y-3">
                <div className="w-12 h-12 mx-auto bg-blue-50 rounded-full flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-blue-500" />
                </div>
                <div>
                  <p className="text-gray-600 font-medium">开始对话</p>
                  <p className="text-gray-500 text-sm">与AI深入探讨内容，获得更多见解</p>
                </div>
              </div>
            </div>
          ) : (
            <>
              {chatMessages.map((message) => {
                if (!message.id || !message.content) {
                  return null
                }

                return (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[85%] px-4 py-3 rounded-2xl text-sm shadow-sm ${
                        message.role === 'user'
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                          : 'bg-white border border-gray-200 text-gray-900'
                      }`}
                    >
                      {message.role === 'assistant' ? (
                        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-code:text-blue-600 prose-code:bg-blue-50">
                          {message.content || '正在思考...'}
                        </SafeMarkdown>
                      ) : (
                        <span>{message.content}</span>
                      )}
                    </div>
                  </div>
                )
              }).filter(Boolean)}
              
              {/* 流式输入指示器 */}
              {isStreaming && (
                <div className="flex justify-start">
                  <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">AI正在思考...</span>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* 聊天输入 - 现代化设计 */}
        <div className="flex-shrink-0 p-6 border-t border-gray-200/50 bg-white/80 backdrop-blur-sm">
          <form onSubmit={handleChatSubmit} className="flex space-x-3">
            <div className="flex-1 relative">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                placeholder="与AI深入探讨内容..."
                disabled={isStreaming}
                className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 outline-none text-sm bg-white transition-all duration-200 shadow-sm hover:shadow-md disabled:bg-gray-50 disabled:text-gray-500"
              />
            </div>
            <button
              type="submit"
              disabled={!chatInput.trim() || isStreaming}
              className="px-5 py-3 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-2xl hover:from-purple-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
            >
              <Send size={16} />
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default AIAssistant
